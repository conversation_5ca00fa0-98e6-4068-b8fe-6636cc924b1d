import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AuditLog } from './schemas/audit-log.schema';
import { SortOrder } from 'mongoose'
import { RadiologyLog } from './schemas/radiology-log.schema';

export enum SERVICE {
  AUDIT_LOG = 1,
  RADIOLOGY_LOG = 2,
}

@Injectable()
export class LogsService {
  constructor(
    @InjectModel(AuditLog.name) private readonly auditLogModel: Model<AuditLog>,
    @InjectModel(RadiologyLog.name, 'radiologyConnection') 
    private readonly radiologyLogModel: Model<RadiologyLog>,
  ) {}

  async createLog(log: Partial<AuditLog>, serviceKey: number): Promise<AuditLog> {
    
    //Implementation here
    const serviceHandlers = {
      [SERVICE.AUDIT_LOG]: this.auditlogsDb.bind(this),
      [SERVICE.RADIOLOGY_LOG]: this.radiologylogsDb.bind(this),
    };
  
    const handler = serviceHandlers[serviceKey];
  
    if (!handler) {
      throw new Error(`Invalid service key: ${serviceKey}`);
    }
  
    try {
      return await handler(log);
    } catch (error) {
      Logger.error(`Error while inserting log for serviceKey ${serviceKey}: ${error.message}`);
      throw error;
    }

  }

  //Implemetation here
  private auditlogsDb(log: any){
    try {
      Logger.log('Inserting AuditLogs')
      const newLog = new this.auditLogModel(log);
      return newLog.save();

    } catch (error) {
      Logger.error("Error While Inserting AuditlogsDb")
    }
  }


  private radiologylogsDb(log: any){
    try {
      Logger.log('Inserting RadioLogy Log')
      const newLog = new this.radiologyLogModel(log);
      return newLog.save();
      
    } catch (error) {
      Logger.error("Error While Inserting RadioLogyDB")
    }
  }

  async getLogs(filter: Record<string, string | undefined>): Promise<AuditLog> {
    return this.auditLogModel.findOne(filter).exec();
  }

  async getLogsByResource(resource: string): Promise<AuditLog> {
    return this.auditLogModel.findOne({ resource }).exec();
  }

  async findWithDynamicFilters(
    filters: Record<string, any>,
    skip = 0,
    limit = 10,
    sort = '-createdAt', // Default to descending order
    serviceKey: number,
  ): Promise<{
    data: AuditLog[];
    totalCount: number;
    nextPageAvailable: boolean;
    previousPageAvailable: boolean;
  }> {
    const query: any = {};
    console.log("Filters:", filters);

    // console.log("isMobile ",filters.isMobile);

    // Handle date range filtering
    if (filters.fromDate || filters.toDate) {
      query.createdAt = {};
      if (filters.fromDate && !isNaN(Date.parse(filters.fromDate))) {
        query.createdAt.$gte = new Date(filters.fromDate);
      }
      if (filters.toDate && !isNaN(Date.parse(filters.toDate))) {
        query.createdAt.$lte = new Date(filters.toDate);
      }
    }

    // Handle `isMobile` filter separately
    if (filters.isMobile && filters.isMobile !== "") {
      query.isMobile = filters.isMobile;
    }
  
    // Handle general filters dynamically
    for (const key in filters) {
      if (['fromDate', 'toDate', 'search', 'isMobile', 'usernameSearchTerm', 'metadataSearchTerm'].includes(key)) continue;

      if (key.includes('.')) {
        query[key] = { $regex: filters[key], $options: 'i' }; // Case-insensitive search for nested fields
      } else {
        query[key] = filters[key];
      }
    }
  
    // Handle search parameter (keep for backward compatibility)
    if (filters.search) {
      const searchRegex = { $regex: filters.search, $options: 'i' };
      query.$or = [
        { userName: searchRegex },
        { userType: searchRegex },
        { action: searchRegex },
        { resource: searchRegex },
        { message: searchRegex },
        // Search within all string values in metadata with null check
        {
          $and: [
            { 'metadata': { $exists: true, $type: 'object' } },
            {
              $expr: {
                $gt: [
                  {
                    $size: {
                      $filter: {
                        input: { $objectToArray: '$metadata' },
                        as: 'item',
                        cond: {
                          $and: [
                            { $eq: [{ $type: '$$item.v' }, 'string'] },
                            { $regexMatch: { input: '$$item.v', regex: filters.search, options: 'i' } }
                          ]
                        }
                      }
                    }
                  },
                  0
                ]
              }
            }
          ]
        },
      ];
    }

    // Handle the two new search types
    const searchConditions = [];

    // Username search - searches only in username-related fields
    if (filters.usernameSearchTerm) {
      const usernameSearchRegex = { $regex: filters.usernameSearchTerm, $options: 'i' };
      searchConditions.push({
        $or: [
          { userName: usernameSearchRegex },
          { userId: usernameSearchRegex },
        ]
      });
    }

    // Metadata search - searches only in metadata fields
    if (filters.metadataSearchTerm) {
      searchConditions.push({
        $and: [
          { 'metadata': { $exists: true, $type: 'object' } },
          {
            $expr: {
              $gt: [
                {
                  $size: {
                    $filter: {
                      input: { $objectToArray: '$metadata' },
                      as: 'item',
                      cond: {
                        $and: [
                          { $eq: [{ $type: '$$item.v' }, 'string'] },
                          { $regexMatch: { input: '$$item.v', regex: filters.metadataSearchTerm, options: 'i' } }
                        ]
                      }
                    }
                  }
                },
                0
              ]
            }
          }
        ]
      });
    }

    // Combine search conditions with AND logic (all searches must match)
    if (searchConditions.length > 0) {
      if (query.$or) {
        // If there's already an $or condition from the old search, combine them
        query.$and = [{ $or: query.$or }, ...searchConditions];
        delete query.$or;
      } else {
        // If there are multiple search conditions, combine them with AND
        if (searchConditions.length === 1) {
          Object.assign(query, searchConditions[0]);
        } else {
          query.$and = searchConditions;
        }
      }
    }

    
  
    // Ensure descending order by `createdAt`
    const sortOption: Record<string, SortOrder> = { createdAt: -1 as SortOrder };
  
    //Implement it here for switching
  
    const serviceHandlers = {
      [SERVICE.AUDIT_LOG]: this.audiltLogsSearch.bind(this),
      [SERVICE.RADIOLOGY_LOG]: this.radiologyLogsSearch.bind(this),
    };
  
    // Get the appropriate search handler based on the serviceKey
    const handler = serviceHandlers[serviceKey];
  
    if (!handler) {
      throw new Error(`Invalid service key: ${serviceKey}`);
    }
  
    try {
      
      const data = await handler(query, skip, limit, sortOption);
  
      return {
        data: data.data,
        totalCount: data.totalCount,
        nextPageAvailable: skip + limit < data.totalCount,
        previousPageAvailable: skip > 0,
      };
    } catch (error) {
      Logger.error(`Error while searching logs for serviceKey ${serviceKey}: ${error.message}`);
      throw error;
    }
  }

  private async audiltLogsSearch(query: any, skip: number, limit: number, sortOption: Record<string, SortOrder>){
    try {
      Logger.log("searching auditLogs")
      const totalCount = await this.auditLogModel.countDocuments(query);

      const data = await this.auditLogModel
      .find(query)
      .skip(skip)
      .limit(limit)
      .sort(sortOption) // Apply sorting correctly
      .exec();

      Logger.log("Search Complete on auditLogs")
      return { data, totalCount };
    } catch (error) {
      Logger.error("error while searching in auditLogs")
    }
  }

  private async radiologyLogsSearch(query: any, skip: number, limit: number, sortOption: Record<string, SortOrder>){
    try {
      
      Logger.log("searching radiology")

      const totalCount = await this.radiologyLogModel.countDocuments(query);

      const data = await this.radiologyLogModel
     .find(query)
     .skip(skip)
     .limit(limit)
     .sort(sortOption) // Apply sorting correctly
     .exec();

      Logger.log("Search Complete on radiology")
      return { data, totalCount };
    } catch (error) {
      Logger.error("error while searching in radiology")
    }
  }
  
  async getSchoolFilter(serviceKey: string): Promise<any> {
    const serviceHandlers = {
      [SERVICE.AUDIT_LOG]: () => this.auditLogModel,
      [SERVICE.RADIOLOGY_LOG]: () => this.radiologyLogModel,
    };

    const handler = serviceHandlers[serviceKey];
    if (!handler) {
      throw new Error('Invalid service key');
    }

    const model = handler();
    const schoolFilter = await model.aggregate([
      {
        $match: {
          $and: [
            { schoolId: { $ne: null } }, 
            { schoolId: { $ne: "" } }, 
            { schoolName: { $ne: null } }, 
            { schoolName: { $ne: "" } },
            {
              $expr: { $ne: [{ $toInt: "$schoolId" }, 0] } // Convert schoolId to int and check if it's not 0
            }
          ]
        }
      },
      {
        $group: {
          _id: { schoolId: "$schoolId", schoolName: "$schoolName" },
        },
      },
      {
        $project: {
          _id: 0,
          schoolId: "$_id.schoolId",
          schoolName: "$_id.schoolName",
        },
      },
    ]);
  
    return schoolFilter;
  }
  
  async getActionFilter(serviceKey: string): Promise<any> {
    const serviceHandlers = {
      [SERVICE.AUDIT_LOG]: () => this.auditLogModel,
      [SERVICE.RADIOLOGY_LOG]: () => this.radiologyLogModel,
    };

    const handler = serviceHandlers[serviceKey];
    if (!handler) {
      throw new Error('Invalid service key');
    }

    const model = handler();
    const actionFilter = await model.aggregate([
      {
        $group: {
          _id: { action: '$action' },
        },
      },
      {
        $project: {
          _id: 0,
          action: '$_id.action',
        },
      },
    ]);
    return actionFilter;
  }

  async getResourceFilter(serviceKey: string): Promise<any> {
    const serviceHandlers = {
      [SERVICE.AUDIT_LOG]: () => this.auditLogModel,
      [SERVICE.RADIOLOGY_LOG]: () => this.radiologyLogModel,
    };

    const handler = serviceHandlers[serviceKey];
    if (!handler) {
      throw new Error('Invalid service key');
    }

    const model = handler();
    const resourceFilter = await model.aggregate([
      {
        $group: {
          _id: { resource: '$resource' },
        },
      },
      {
        $project: {
          _id: 0,
          resource: '$_id.resource',
        },
      },
    ]);
    return resourceFilter;
  }

async getUserFilter(serviceKey: string, schoolId?: string): Promise<any[]> {
    const serviceHandlers = {
      [SERVICE.AUDIT_LOG]: () => this.auditLogModel,
      [SERVICE.RADIOLOGY_LOG]: () => this.radiologyLogModel,
    };

    const handler = serviceHandlers[serviceKey];
    if (!handler) {
      throw new Error('Invalid service key');
    }

    try {
      const model = handler();
      const matchConditions: any[] = [
        { userName: { $exists: true } },
        { userName: { $ne: null } },
        { userName: { $ne: "" } }
      ];

      if (schoolId) {
        matchConditions.push({ schoolId: schoolId });
      }

      const userFilter = await model.aggregate([
        {
          $match: {
            $and: matchConditions
          }
        },
        {
          $group: {
            _id: '$userId', // Group only by userId to get unique users
            userName: { $first: '$userName' },
            userType: { $first: '$userType' }
          },
        },
        {
          $project: {
            _id: 0,
            userId: '$_id',
            userName: 1,
            userType: 1
          },
        },
        {
          $sort: {
            userId: 1
          }
        }
      ]);

      return userFilter;
    } catch (error) {
      Logger.error(`Error fetching user filter for serviceKey ${serviceKey}: ${error.message}`);
      throw error;
    }
  }
}
