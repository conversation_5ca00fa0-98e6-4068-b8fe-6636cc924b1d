export type MetadataType = {
  rotationId?: string;
  parentRotationId?: string;
  schoolId?: string;
  locationId?: string;
  title?: string;
  hospitalSiteId?: string;
  courseId?: string;
  startDate?: string;
  endDate?: string;
  isActive?: string;
  createdBy?: string;
  createdDate?: string;
  updatedBy?: string;
  updatedDate?: string;
  duration?: string;
  isDelete?: string;
  deleteDate?: string | null;
  deletedUserId?: string | null;
  isSchedule?: string;
  rotationName?: string;
  hospitalSiteName?: string;
  schoolName?: string;
  courseName?: string;
  parentRotationName?: string | null;
  additionalData?: {
    students?: Array<{
      rotationDetailsId: string;
      studentId: string;
      schoolClinicalSiteUnitId: string;
      rotationId: string;
      rotationName: string;
      studentName: string;
    }>;
  };
  superAdmin?: {
    superAdminId: string;
    name: string;
  };
};

export type AuditLogType = {
  _id: string;
  userId: string;
  action: string;
  resource: string;
  metadata: MetadataType;
  resourceId: string;
  schoolId: string;
  schoolName: string;
  userType: string;
  userName: string;
  rotationName: string;
  message: string;
  ipAddress: string;
  isMobile: string;
  createdAt: string;
  updatedAt: string;
};
export type dataType={
  data:AuditLogType[]
}
export type DateRangeType = 'selectdays' |'today' | 'yesterday' | 'last7days' | 'custom';







export  type FilterTypes = 
  {
    schoolId?: string;
    resource?: string;
    action?: string;
    fromDate?: string;
    toDate?: string;
    search?: string;
    state?: string;
    isMobile?: string; // Ensure this is using isMobile, not deviceType
    userName?: string;
    userId?: string;

  }
