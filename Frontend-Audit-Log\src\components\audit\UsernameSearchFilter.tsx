import { Input } from '@/components/ui/input';
import { PersonIcon } from '@radix-ui/react-icons';

interface UsernameSearchFilterProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
  placeholder?: string;
}

export function UsernameSearchFilter({
  searchQuery,
  onSearchChange,
  placeholder = "Search by username..."
}: UsernameSearchFilterProps) {
  return (
    <div className="space-y-1">
      <h3 className="text-md font-medium">Username Search</h3>
      <div className="relative">
        <PersonIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder={placeholder}
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          onPaste={(e) => {
            e.preventDefault();
            const pastedText = e.clipboardData.getData("text").trim();
            onSearchChange(pastedText);
          }}
          className="pl-10 bg-white"
        />
      </div>
      <p className="text-xs text-gray-500">
        Search specifically in username and user ID fields
      </p>
    </div>
  );
}
