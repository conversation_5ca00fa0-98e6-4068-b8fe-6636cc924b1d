import { useState, useEffect, useCallback, useMemo } from 'react';
import { AuditLogType } from '@/types/audit';
import { checkSessionStatus, createSessionExpiredModal, fetchAuditLogsWithAuth, getQueryParams, getStoredTokens, showSessionExpiredModal } from '@/services/api';
import { startOfDay, subDays, endOfDay } from 'date-fns';
import { DateRange } from 'react-day-picker';

export function useAuditLogs() {
    const [logs, setLogs] = useState<AuditLogType[]>([]);
    const [resources, setResources] = useState<string[]>([]);
    const [actions, setActions] = useState<string[]>([]);
    const [schools, setSchools] = useState<Array<{ schoolId: string; schoolName: string }>>([]);    
    const [users, setUsers] = useState<Array<{ userId: string; userName: string }>>([]);
    const [selectedUser, setSelectedUser] = useState('all');
    const [isLoading, setIsLoading] = useState(true);
    const [isAuthorizing, setIsAuthorizing] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [sessionExpired, setSessionExpired] = useState(false);
    const [hasCheckedAuth, setHasCheckedAuth] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [rowsPerPage, setRowsPerPage] = useState(50);
    const [selectedSchool, setSelectedSchool] = useState('all');
    const [selectedAction, setSelectedAction] = useState('all');
    const [selectedDeviceType, setSelectedDeviceType] = useState('all');
    const [searchQuery, setSearchQuery] = useState('');
    const [usernameSearchQuery, setUsernameSearchQuery] = useState('');
    const [metadataSearchQuery, setMetadataSearchQuery] = useState('');
    const [dateRange, setDateRange] = useState<DateRange>({
        from: startOfDay(subDays(new Date(), 7)),
        to: endOfDay(new Date())
    });

    const fetchFilters = useCallback(async () => {
        try {
            const { accessToken } = getStoredTokens();
            const queryParams = getQueryParams();
            const { state } = queryParams;

            let serviceKey;
            if (state === 'CT-RT') {
                serviceKey = 1;
            } else if (state === 'CT-RAD') {
                serviceKey = 2;
            } else {
                throw new Error("Invalid state parameter, cannot determine serviceKey.");
            }
            if (!accessToken) throw new Error('No access token available');

            const response = await fetch(`http://localhost:3000/logs/filters-list?serviceKey=${serviceKey}`, {
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                },
            });

            if (!response.ok) {
                throw new Error('Failed to fetch filters');
            }

            const data = await response.json();
            if (data.statusCode === 200) {
                setResources(data.filters.resourceFilter.map((r: { resource: string }) => r.resource));
                setActions(data.filters.actionFilter.map((a: { action: string }) => a.action));
                setSchools(data.filters.schoolFilter.filter((s: { schoolId: string; schoolName: string }) =>
                    s.schoolId && s.schoolName
                ));
                setUsers(data.filters.userFilter.filter((s: { userId: string; userName: string }) =>
                    s.userId && s.userName
                ));
            }
        } catch (error) {
            console.error('Error fetching filters:', error);
            if (error instanceof Error && error.message.includes('401')) {
                setSessionExpired(true);
            }
        }
    }, []);

    const fetchLogs = useCallback(
        async (page = currentPage, perPage = rowsPerPage, schoolId = selectedSchool, action = selectedAction, userId = selectedUser, deviceType = selectedDeviceType, usernameSearch = usernameSearchQuery, metadataSearch = metadataSearchQuery, generalSearch = searchQuery) => {
            try {
                setIsLoading(true);
                setError(null);

                const sessionStatus = checkSessionStatus();
                if (!sessionStatus.isValid) {
                    setSessionExpired(true);
                    setHasCheckedAuth(true);
                    setIsLoading(false);
                    return;
                }

                await fetchFilters();

                const filters: any = {
                    schoolId: schoolId === 'all' ? '0' : schoolId,
                    action: action !== 'all' ? action : undefined,
                    userId: userId !== 'all' ? userId : undefined,
                    fromDate: dateRange.from?.toISOString(),
                    toDate: dateRange.to?.toISOString(),
                    isMobile: deviceType // Send as isMobile, not deviceType
                };

                if (generalSearch) {
                    filters.search = generalSearch;
                }

                if (usernameSearch) {
                    filters.usernameSearch = usernameSearch;
                }

                if (metadataSearch) {
                    filters.metadataSearch = metadataSearch;
                }

                const logsData = await fetchAuditLogsWithAuth(page, perPage, filters);

                if (Array.isArray(logsData?.data)) {
                    setLogs(logsData.data);
                }
            } catch (error: any) {
                if (error?.response?.status === 401) {
                    setSessionExpired(true);
                    showSessionExpiredModal(
                        document.getElementById('session-expired-modal') || createSessionExpiredModal()
                    );
                } else {
                    setError(error instanceof Error ? error.message : 'Failed to load audit logs.');
                }
                setLogs([]);
            } finally {
                setIsLoading(false);
                setIsAuthorizing(false);
                setHasCheckedAuth(true);
            }
        },
        [currentPage, rowsPerPage, selectedSchool, selectedAction, selectedUser, selectedDeviceType, dateRange, searchQuery, usernameSearchQuery, metadataSearchQuery, fetchFilters]
    );

    useEffect(() => {
        if (hasCheckedAuth && !isAuthorizing) {
            fetchLogs();
        }
    }, [dateRange, selectedDeviceType, fetchLogs, hasCheckedAuth, isAuthorizing, selectedUser]); // Removed search queries from dependencies

    useEffect(() => {
        const initializeData = async () => {
            await new Promise(resolve => setTimeout(resolve, 800));

            if (document.getElementById('session-expired-modal')?.style.display === 'block') {
                setSessionExpired(true);
                setIsAuthorizing(false);
                setHasCheckedAuth(true);
                setIsLoading(false);
                return;
            }

            setIsAuthorizing(true);
            await fetchLogs().catch(e => {
                console.error("Error during initial fetch:", e);
            });
        };

        if (window.location.pathname === '/dashboard') {
            initializeData();
        } else {
            setIsAuthorizing(false);
            setHasCheckedAuth(true);
            setIsLoading(false);
        }
    }, [fetchLogs]);

    const refreshLogs = useCallback(async () => {
        const sessionStatus = checkSessionStatus();
        if (!sessionStatus.isValid) {
            setSessionExpired(true);
            showSessionExpiredModal(
                document.getElementById('session-expired-modal') || createSessionExpiredModal()
            );
            return;
        }
        await fetchLogs();
    }, [fetchLogs]);

    // Function to trigger search when search buttons are clicked
    const triggerSearch = useCallback(async () => {
        await fetchLogs();
    }, [fetchLogs]);

    // Function to trigger search with specific parameters (bypasses state)
    const triggerSearchWithParams = useCallback(async (generalSearch: string, usernameSearch: string, metadataSearch: string) => {
        await fetchLogs(currentPage, rowsPerPage, selectedSchool, selectedAction, selectedUser, selectedDeviceType, usernameSearch, metadataSearch, generalSearch);
    }, [fetchLogs, currentPage, rowsPerPage, selectedSchool, selectedAction, selectedUser, selectedDeviceType]);

    const handleDateRangeChange = (newDateRange: DateRange | undefined) => {
        if (newDateRange) {
            setDateRange(newDateRange);
        }
    };

    return {
        logs,
        resources,
        actions,
        schools,
        isLoading,
        isAuthorizing,
        hasCheckedAuth,
        error,
        sessionExpired,
        refreshLogs,
        selectedSchool,
        setSelectedSchool,
        selectedAction,
        setSelectedAction,
        currentPage,
        rowsPerPage,
        dateRange,
        setDateRange: handleDateRangeChange,
        searchQuery,
        setSearchQuery,
        usernameSearchQuery,
        setUsernameSearchQuery,
        metadataSearchQuery,
        setMetadataSearchQuery,
        selectedDeviceType,
        setSelectedDeviceType,
        users,
        selectedUser,
        setSelectedUser,
        triggerSearch,
        triggerSearchWithParams
    };
}
