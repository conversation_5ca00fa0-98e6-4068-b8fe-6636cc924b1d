import { useState, useMemo, useEffect } from 'react';
import { DateRange } from 'react-day-picker';
import { addDays, startOfYesterday, endOfYesterday, startOfDay, endOfDay, isWithinInterval } from 'date-fns';
import { AuditLogType, DateRangeType } from '@/types/audit';

export function useAuditLogFilters(initialLogs: AuditLogType[]) {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedResource, setSelectedResource] = useState('all');
    const [selectedAction, setSelectedAction] = useState('all');
    const [selectedSchool, setSelectedSchool] = useState('all');
    const [selectedDeviceType, setSelectedDeviceType] = useState('all');
    const [selectedUser, setSelectedUser] = useState('all'); // Add selectedUser state

    const now = new Date();
    const [dateRange, setDateRange] = useState<DateRange | undefined>({
        from: startOfDay(addDays(now, -6)),
        to: endOfDay(now)
    });
    const [selectedRange, setSelectedRange] = useState<DateRangeType>('last7days');

    useEffect(() => {
        handleRangeTypeChange('last7days');
    }, []);

    const resetDateFilters = () => {
        setDateRange(undefined);
        setSelectedRange('selectdays');
    };

    const handleDateRangeChange = (range: DateRange | undefined) => {
        if (!range) {
            setDateRange(undefined);
            handleRangeTypeChange('selectdays');
        } else {
            setDateRange({
                from: startOfDay(range.from!),
                to: range.to ? endOfDay(range.to) : undefined
            });

            if (selectedRange !== 'custom') {
                handleRangeTypeChange('custom');
            }
        }
    };

    const handleRangeTypeChange = (type: DateRangeType) => {
        setSelectedRange(type);

        const now = new Date();
        switch (type) {
            case 'today':
                setDateRange({ from: startOfDay(now), to: endOfDay(now) });
                break;
            case 'yesterday':
                setDateRange({ from: startOfYesterday(), to: endOfYesterday() });
                break;
            case 'last7days':
                setDateRange({ from: startOfDay(addDays(now, -6)), to: endOfDay(now) });
                break;
            case 'custom':
                break;
            default:
                setDateRange(undefined);
                break;
        }
    };

    const filteredLogs = useMemo(() => {
        if (!Array.isArray(initialLogs)) return [];

        return initialLogs.filter((log) => {
            if (!log) return false;

            const message = log.message?.toLowerCase() ?? '';
            const userName = log.userName?.toLowerCase() ?? '';
            const userType = log.userType?.toLowerCase() ?? '';
            const resource = log.resource?.toLowerCase() ?? '';
            const searchTerm = searchQuery.toLowerCase();

            const matchesSearch = !searchQuery ||
                message.includes(searchTerm) ||
                userType.includes(searchTerm) ||
                resource.includes(searchTerm) ||
                userName.includes(searchTerm);

            const matchesResource = selectedResource === 'all' || log.resource === selectedResource;
            const matchesAction = selectedAction === 'all' || log.action === selectedAction;
            const matchesSchool = selectedSchool === 'all' || log.schoolName === selectedSchool;
            const matchesDeviceType = selectedDeviceType === 'all' ||
                log.isMobile === selectedDeviceType;
// In the filteredLogs useMemo
const matchesUser = selectedUser === 'all' || log.userId === selectedUser;
            let matchesDate = true;
            if (dateRange?.from) {
                const logDate = new Date(log.createdAt);
                if (!isNaN(logDate.getTime())) {
                    matchesDate = isWithinInterval(logDate, {
                        start: dateRange.from,
                        end: dateRange.to || new Date()
                    });
                }
            }

            return matchesSearch && matchesSchool && matchesResource && matchesAction && matchesDate && matchesDeviceType && matchesUser; // Include user filter
        });
    }, [initialLogs, searchQuery, selectedResource, selectedSchool, selectedAction, dateRange, selectedDeviceType, selectedUser]); // Add selectedUser to dependency array

    return {
        searchQuery,
        setSearchQuery,
        selectedResource,
        setSelectedResource,
        selectedAction,
        setSelectedAction,
        selectedSchool,
        setSelectedSchool,
        dateRange,
        setDateRange: handleDateRangeChange,
        selectedRange,
        handleRangeTypeChange,
        filteredLogs,
        resetDateFilters,
        selectedDeviceType,
        setSelectedDeviceType,
        selectedUser, // Export selectedUser
        setSelectedUser, // Export setSelectedUser
    };
}
