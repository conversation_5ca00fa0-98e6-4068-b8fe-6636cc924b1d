import { useState, useEffect, useMemo } from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { EyeOpenIcon, ReloadIcon, MagnifyingGlassIcon, PersonIcon, FileTextIcon } from '@radix-ui/react-icons';
import { format } from 'date-fns';
import { AuditLogType, DateRangeType } from '@/types/audit';
import { AuditLogDetailsSheet } from './AuditLogDetailsSheet';
import { Skeleton } from '@/components/ui/skeleton';
import {
    Select,
    SelectTrigger,
    SelectValue,
    SelectContent,
    SelectItem,
} from '@/components/ui/select';
import { getQueryParams, fetchAuditLogsWithAuth } from '@/services/api';
import { DateRange } from 'react-day-picker';

interface PaginationInfo {
    totalCount: number;
    nextPageAvailable: boolean;
    previousPageAvailable: boolean;
}

interface AuditLogTableProps {
    logs: AuditLogType[];
    searchQuery: string;
    onSearchChange: (value: string) => void;
    usernameSearchQuery: string;
    onUsernameSearchChange: (value: string) => void;
    metadataSearchQuery: string;
    onMetadataSearchChange: (value: string) => void;
    selectedRange: DateRangeType;
    onRangeChange: (type: string) => void;
    isLoading: boolean;
    selectedResource?: string;
    selectedAction?: string;
    onClearResource?: () => void;
    selectedSchool?: string;
    onRefresh?: () => Promise<void>;
    dateRange?: DateRange;
    selectedDeviceType: string;
    schools?: Array<{ schoolId: string; schoolName: string }>;
    User?: Array<{ userId: string; userName: string }>;
    selectedUser?: string; // Add selectedUser prop
}

export function AuditLogTable({
    logs,
    searchQuery,
    onSearchChange,
    usernameSearchQuery,
    onUsernameSearchChange,
    metadataSearchQuery,
    onMetadataSearchChange,
    onRangeChange,
    selectedSchool,
    isLoading,
    selectedRange,
    selectedResource,
    selectedAction,
    dateRange,
    selectedDeviceType,
    schools,
    selectedUser, // Destructure selectedUser
    User
}: AuditLogTableProps) {
    const [selectedLog, setSelectedLog] = useState<AuditLogType | null>(null);
    const [detailsOpen, setDetailsOpen] = useState(false);
    const [page, setPage] = useState(1);
    const [rowsPerPage, setRowsPerPage] = useState(50);
    const [localSelectedRange, setLocalSelectedRange] = useState<DateRangeType>('selectdays');
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [paginationInfo, setPaginationInfo] = useState<PaginationInfo>({
        totalCount: 0,
        nextPageAvailable: false,
        previousPageAvailable: false
    });
    const [tableData, setTableData] = useState<AuditLogType[]>([]);

    // Local state for search inputs (not triggering API until search button is clicked)
    const [localUsernameSearch, setLocalUsernameSearch] = useState('');
    const [localMetadataSearch, setLocalMetadataSearch] = useState('');

    // Search handlers
    const handleUsernameSearch = () => {
        onUsernameSearchChange(localUsernameSearch);
    };

    const handleMetadataSearch = () => {
        onMetadataSearchChange(localMetadataSearch);
    };

    const handleClearUsernameSearch = () => {
        setLocalUsernameSearch('');
        onUsernameSearchChange('');
    };

    const handleClearMetadataSearch = () => {
        setLocalMetadataSearch('');
        onMetadataSearchChange('');
    };

    const fetchTableData = async () => {
        setIsRefreshing(true);
        try {
            const schoolFilter = selectedSchool && selectedSchool !== 'all' ? { schoolId: selectedSchool } : {};
            const resourceFilter = selectedResource && selectedResource !== 'all' ? { resource: selectedResource } : {};
            const actionFilter = selectedAction && selectedAction !== 'all' ? { action: selectedAction } : {};
            const deviceTypeFilter = selectedDeviceType && selectedDeviceType !== 'all' ? { isMobile: selectedDeviceType } : {};
            const searchFilter = searchQuery ? { search: searchQuery } : {};
            const userFilter = selectedUser && selectedUser !== 'all' ? { userId: selectedUser } : {}; 

            const dateRangeFilter = dateRange?.from ? {
                fromDate: dateRange.from.toISOString(),
                toDate: dateRange.to ? dateRange.to.toISOString() : dateRange.from.toISOString()
            } : {};

            const filters = {
                ...schoolFilter,
                ...resourceFilter,
                ...searchFilter,
                ...actionFilter,
                ...dateRangeFilter,
                ...deviceTypeFilter,
                ...userFilter, // Include userFilter in the request
            };

            const response = await fetchAuditLogsWithAuth(page, rowsPerPage, filters);

            if (response && response.data) {
                const sortedLogs = response.data.sort((a, b) => {
                    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
                });

                setTableData(sortedLogs);
                setPaginationInfo({
                    totalCount: response.totalCount,
                    nextPageAvailable: response.nextPageAvailable,
                    previousPageAvailable: response.previousPageAvailable
                });
            }
        } catch (error) {
            console.error('Error fetching table data:', error);
        } finally {
            setIsRefreshing(false);
        }
    };

    useEffect(() => {
        fetchTableData();
    }, [page, rowsPerPage, selectedSchool, selectedResource, selectedAction, searchQuery, dateRange, selectedDeviceType, selectedUser]); // Add selectedUser to dependency array

    useEffect(() => {
        setPage(1); // Reset to first page when filters change
    }, [selectedSchool, selectedResource, searchQuery, selectedAction, dateRange, selectedDeviceType, selectedUser]); // Add selectedUser to dependency array

    const handleRefresh = () => {
        fetchTableData();
    };

    const totalPages = Math.ceil(paginationInfo.totalCount / rowsPerPage);
    const startRecord = (page - 1) * rowsPerPage + 1;
    const endRecord = Math.min(page * rowsPerPage, paginationInfo.totalCount);

    useEffect(() => {
        setLocalSelectedRange(selectedRange);
    }, [selectedRange]);

    const handleRangeChange = (value: string) => {
        const selectedRange = value as DateRangeType;
        setLocalSelectedRange(selectedRange);
        onRangeChange(selectedRange);
    };

    useEffect(() => {
        if (!localSelectedRange) {
            setLocalSelectedRange('selectdays');
            onRangeChange('selectdays');
        }
    }, [localSelectedRange, onRangeChange]);

    const getActionColor = (action: string) => ({
        Delete: 'bg-red-100 text-red-700',
        Add: 'bg-green-100 text-green-800',
        Login: 'bg-purple-500',
    }[action] || 'bg-gray-400');

    const queryParams = getQueryParams();
    const { school_id, state } = queryParams;
    const isSchoolFilterVisible = school_id && school_id !== '0';

    const schoolName = useMemo(() => {
        if (selectedSchool && selectedSchool !== 'all') {
            const school = schools?.find(school => school.schoolId === selectedSchool);
            return school?.schoolName || selectedSchool;
        } else if (isSchoolFilterVisible && logs.length > 0) {
            const logWithSchool = logs.find(log => log?.schoolName);
            return logWithSchool?.schoolName || 'School Information Not Available';
        }
        return null;
    }, [logs, selectedSchool, isSchoolFilterVisible, schools]);

    // Determine the background color and text color based on the state
    const tableHeaderBgColor = state === 'CT-RT' ? '#0DE273' : 'rgba(46, 49, 146, 1)';
    const tableHeaderTextColor = state === 'CT-RT' ? 'black' : 'white';

    return (
        <div className="flex flex-col w-full h-full max-h-screen">
            <div className='text-center'>
                {isSchoolFilterVisible && schoolName && (
                    <div className="p-1 text-sm mb-2 text-black flex text-center">
                        School Name: <h6 className="ml-2 font-semibold">{schoolName}</h6>
                    </div>
                )}
                {selectedSchool && selectedSchool !== 'all' && schoolName && (
                    <div className="mb-2 text-sm font-semibold text-center justify-center  text-black flex items-center">
                        School Name: <p className="ml-2">{schoolName}</p>
                    </div>
                )}
            </div>

            {/* Three Search Inputs */}
            <div className="mb-3 flex flex-col gap-3">
                {/* General Search */}
                <div className="flex items-center gap-2">
                    <div className="relative flex-1">
                        <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                            placeholder="General search (searches across all fields)..."
                            value={searchQuery}
                            onChange={(e) => onSearchChange(e.target.value)}
                            onPaste={(e) => {
                                e.preventDefault();
                                const pastedText = e.clipboardData.getData("text").trim();
                                onSearchChange(pastedText);
                            }}
                            className="pl-10 bg-white"
                        />
                    </div>
                </div>

                {/* Username Search */}
                <div className="flex items-center gap-2">
                    <div className="relative flex-1">
                        <PersonIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                            placeholder="Search by username or user ID..."
                            value={localUsernameSearch}
                            onChange={(e) => setLocalUsernameSearch(e.target.value)}
                            onPaste={(e) => {
                                e.preventDefault();
                                const pastedText = e.clipboardData.getData("text").trim();
                                setLocalUsernameSearch(pastedText);
                            }}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                    handleUsernameSearch();
                                }
                            }}
                            className="pl-10 bg-white"
                        />
                    </div>
                    <Button
                        onClick={handleUsernameSearch}
                        disabled={isRefreshing}
                        className="flex items-center bg-blue-500 hover:bg-blue-600 text-white gap-1 px-3 py-2 h-10"
                        size="sm"
                    >
                        <MagnifyingGlassIcon className="w-4 h-4" />
                        Search
                    </Button>
                    {usernameSearchQuery && (
                        <Button
                            onClick={handleClearUsernameSearch}
                            disabled={isRefreshing}
                            className="flex items-center bg-gray-500 hover:bg-gray-600 text-white gap-1 px-3 py-2 h-10"
                            size="sm"
                        >
                            Clear
                        </Button>
                    )}
                </div>

                {/* Metadata Search */}
                <div className="flex items-center gap-2">
                    <div className="relative flex-1">
                        <FileTextIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                            placeholder="Search in metadata fields..."
                            value={localMetadataSearch}
                            onChange={(e) => setLocalMetadataSearch(e.target.value)}
                            onPaste={(e) => {
                                e.preventDefault();
                                const pastedText = e.clipboardData.getData("text").trim();
                                setLocalMetadataSearch(pastedText);
                            }}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                    handleMetadataSearch();
                                }
                            }}
                            className="pl-10 bg-white"
                        />
                    </div>
                    <Button
                        onClick={handleMetadataSearch}
                        disabled={isRefreshing}
                        className="flex items-center bg-green-500 hover:bg-green-600 text-white gap-1 px-3 py-2 h-10"
                        size="sm"
                    >
                        <MagnifyingGlassIcon className="w-4 h-4" />
                        Search
                    </Button>
                    {metadataSearchQuery && (
                        <Button
                            onClick={handleClearMetadataSearch}
                            disabled={isRefreshing}
                            className="flex items-center bg-gray-500 hover:bg-gray-600 text-white gap-1 px-3 py-2 h-10"
                            size="sm"
                        >
                            Clear
                        </Button>
                    )}
                </div>
            </div>

            {/* Refresh Button */}
            <div className="mb-3 flex justify-end">
                <div className="flex items-center gap-2">
                    <Button
                        onClick={handleRefresh}
                        disabled={isRefreshing}
                        className="flex items-center bg-white gap-1 px-3 py-1.5 h-9"
                        size="sm"
                    >
                        <span className='text-black text-sm'>Refresh</span>
                        <ReloadIcon className={`w-3.5 h-3.5 text-black ${isRefreshing ? 'animate-spin' : ''}`} />
                    </Button>

                    <Select
                        value={localSelectedRange || "selectdays"}
                        onValueChange={handleRangeChange}
                    >
                        <SelectTrigger className="w-[180px] h-9">
                            <SelectValue placeholder="Select Date Range">
                                {localSelectedRange === 'custom' ? 'Custom Range' : undefined}
                            </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="selectdays">Select Days</SelectItem>
                            <SelectItem value="today">Today</SelectItem>
                            <SelectItem value="yesterday">Yesterday</SelectItem>
                            <SelectItem value="last7days">Last 7 Days</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
            </div>

            <Table className="min-w-full table-fixed">
                <TableHeader className="sticky top-0 z-10" style={{ backgroundColor: tableHeaderBgColor, color: tableHeaderTextColor }}>
                    <TableRow>
                        <TableHead className="w-[150px] px-2 py-2 text-center text-sm font-medium" style={{ color: tableHeaderTextColor }}>Timestamp</TableHead>
                        <TableHead className="w-[100px] px-2 py-2 text-center text-sm font-medium"  style={{ color: tableHeaderTextColor }}>Action</TableHead>
                        <TableHead className="w-[120px] px-2 py-2 text-center text-sm font-medium">Module</TableHead>
                        <TableHead className="w-[120px] px-2 py-2 text-center text-sm font-medium">User</TableHead>
                        <TableHead className="w-[100px] px-2 py-2 text-center text-sm font-medium">User Type</TableHead>
                        <TableHead className="w-[70px] px-2 py-2 text-center text-sm font-medium">Device</TableHead>
                        <TableHead className="w-[200px] px-2 py-2 text-center text-sm font-medium">Message</TableHead>
                        <TableHead className="w-[70px] px-2 py-2 text-center text-sm font-medium">Details</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {isLoading || isRefreshing ? (
                        Array.from({ length: 10 }).map((_, index) => (
                            <TableRow key={`skeleton-${index}`}>
                                <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                                <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                                <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                                <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                                <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                                <TableCell><Skeleton className="h-6 w-6 rounded-full" /></TableCell>
                                <TableCell><Skeleton className="h-4 w-full" /></TableCell>
                                <TableCell><Skeleton className="h-6 w-6" /></TableCell>
                            </TableRow>
                        ))
                    ) : tableData.length === 0 ? (
                        <TableRow>
                            <TableCell colSpan={8} className="h-24 text-center">
                                No audit logs found
                            </TableCell>
                        </TableRow>
                    ) : (
                        tableData.map(log => (
                            <TableRow key={log._id} className="hover:bg-gray-50">
                                <TableCell className="font-mono text-sm p-2 whitespace-nowrap ">
                                    {log.createdAt ? format(new Date(log.createdAt), 'MM-dd-yyyy HH:mm:ss') : 'Invalid Date'}
                                </TableCell>
                                <TableCell className='text-center p-1'>
                                    <Badge className={`${getActionColor(log.action)} inline-flex text-xs px-2 py-0.5 font-medium rounded-full`}>
                                        {log.action}
                                    </Badge>
                                </TableCell>
                                <TableCell className='text-center p-2 text-sm'>{log.resource}</TableCell>
                                <TableCell className='text-center p-2 text-sm'>{log.userName}</TableCell>
                                <TableCell className='p-2 text-sm text-center'>{log.userType}</TableCell>
                                <TableCell className='text-center p-1'>
                                    <img
                                        src={log.isMobile === '1'
                                            ? "/1497619669-androidmobile-phone_85137.png"
                                            : "/32officeicons-31_89708.png"}
                                        alt={log.isMobile === '1' ? "Mobile Device" : "Laptop"}
                                        className="h-5 w-5 mx-auto object-contain"
                                    />
                                </TableCell>
                                <TableCell className='p-2 text-sm max-w-[200px]'>{log.message}</TableCell>
                                <TableCell className='p-1 text-center'>
                                    <Button
                                        size="sm"
                                        className="h-7 w-7 p-0"
                                        onClick={() => {
                                            setSelectedLog(log);
                                            setDetailsOpen(true);
                                        }}
                                    >
                                        <EyeOpenIcon className="h-4 w-4 text-black" />
                                    </Button>
                                </TableCell>
                            </TableRow>
                        ))
                    )}
                </TableBody>
            </Table>

            <div className="flex flex-wrap justify-between items-center gap-2 mt-3 text-sm">
                <Button
                    size="sm"
                    className="text-black h-8 px-3"
                    onClick={() => page > 1 && setPage(page - 1)}
                    disabled={!paginationInfo.previousPageAvailable}
                >
                    Previous
                </Button>

                <span className="text-sm">
                    Showing {startRecord} to {endRecord} of {paginationInfo.totalCount} records
                </span>

                <div className="flex items-center gap-2">
                    <span className="hidden sm:inline text-sm text-gray-600">
                        Rows:
                    </span>
                    <Select
                        value={rowsPerPage.toString()}
                        onValueChange={(value) => {
                            setRowsPerPage(Number(value));
                            setPage(1);
                        }}
                    >
                        <SelectTrigger className="w-[85px] h-8">
                            <SelectValue placeholder="Rows" />
                        </SelectTrigger>
                        <SelectContent>
                            {[50, 100, 150, 200, 500].map((value) => (
                                <SelectItem key={value} value={value.toString()}>{value}</SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>

                <span className="text-sm text-gray-600 hidden sm:block">
                    Total: {paginationInfo.totalCount}
                </span>

                <Button
                    variant="default"
                    className="text-black h-8 px-3"
                    size="sm"
                    onClick={() => page < totalPages && setPage(page + 1)}
                    disabled={!paginationInfo.nextPageAvailable}
                >
                    Next
                </Button>
            </div>

            <AuditLogDetailsSheet
                log={selectedLog}
                open={detailsOpen}
                onOpenChange={setDetailsOpen}
            />
        </div>
    );
}
